import asyncio
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>
from logger_config import logger

class BrowserManager:
    """Playwrightブラウザのライフサイクルとセッションを管理するシングルトンクラス。"""
    _instance = None
    _browser: Browser = None
    _page: Page = None
    _playwright = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(BrowserManager, cls).__new__(cls)
        return cls._instance

    async def start_browser(self, headless=True):
        """ブラウザを起動し、新しいページを作成します。"""
        if self._browser is None:
            logger.info(f"ブラウザを起動します...(headless={headless})")
            self._playwright = await async_playwright().start()
            # Firefoxを使用（他のブラウザも選択可能: p.chromium, p.webkit）
            self._browser = await self._playwright.firefox.launch(headless=headless)
            self._page = await self._browser.new_page()
            logger.info("ブラウザが正常に起動しました。")
        return self._page

    async def get_page(self) -> Page:
        """現在アクティブなページオブジェクトを返します。"""
        if self._page is None or self._page.is_closed():
            logger.warning("ページが利用できません。新しいページを作成します。")
            self._page = await self._browser.new_page()
        return self._page

    async def close_browser(self):
        """ブラウザを安全に閉じます。"""
        if self._browser is not None and self._browser.is_connected():
            logger.info("ブラウザをシャットダウンします...")
            await self._browser.close()
            self._browser = None
            self._page = None
            # Playwrightインスタンスも停止
            if self._playwright is not None:
                await self._playwright.stop()
                self._playwright = None
            logger.info("ブラウザが正常にシャットダウンしました。")

    async def login(self, login_url: str, account: str, password: str):
        """指定されたサイトにログインします。autoroのロジックを参考に実装。"""
        page = await self.get_page()
        logger.info(f"サイトへのログインを開始します: {login_url}")
        
        try:
            # 1. ページに移動
            await page.goto(login_url, wait_until='networkidle', timeout=60000)
            logger.info(f"ページにアクセスしました: {login_url}")

            # 2. ユーザー名を入力
            username_selector = '#josso_username'
            await page.fill(username_selector, account)
            logger.info(f"ユーザー名を入力しました。")

            # 3. パスワードを入力
            password_selector = '#josso_password'
            await page.fill(password_selector, password)
            logger.info("パスワードを入力しました。")

            # 4. ログインボタンをクリックして、ナビゲーションが完了するのを待つ
            login_button_selector = '#form > form > div.submit-container.lastChild > input'
            logger.info("ログインボタンをクリックします...")

            # Playwright async API: 正确等待导航
            async with page.expect_navigation(wait_until='networkidle', timeout=60000):
                await page.click(login_button_selector)

            # ログイン成功の確認（次のページの要素が表示されるのを待つ）
            await page.wait_for_selector('.tab-content', timeout=30000)
            logger.info("ログインに成功し、ダッシュボードが正常に読み込まれました。")

        except Exception as e:
            logger.error(f"ログイン処理中にエラーが発生しました: {e}", exc_info=True)
            # デバッグ用にスクリーンショットを保存
            screenshot_path = 'logs/login_error.png'
            await page.screenshot(path=screenshot_path)
            logger.error(f"エラー発生時のスクリーンショットを {screenshot_path} に保存しました。")
            raise # エラーを再送出してワークフローを停止させる

# グローバルなブラウザマネージャーインスタンス
browser_manager = BrowserManager()