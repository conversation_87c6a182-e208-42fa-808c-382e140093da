import os
import asyncio
from dotenv import load_dotenv
from crewai import Task, Crew
from logger_config import logger
from core.browser.browser_manager import browser_manager
from agents.web_operator_agent import website_operator


from core.gsuite.drive_client import DriveClient
from core.selector_executor import SelectorExecutor

# .envファイルから環境変数を読み込む
load_dotenv()

async def async_run(config: dict):
    """カイポケ月次レポートワークフローを実行します (async 版)。"""
    logger.info("カイポケ月次レポートワークフローを開始します。")

    # ワークフロー全体の設定を取得
    workflow_config = config.get('config', {})
    login_url = workflow_config.get('login_url')
    corporation_id_env = workflow_config.get('corporation_id_env')
    member_login_id_env = workflow_config.get('member_login_id_env')
    password_env = workflow_config.get('password_env')
    download_path = workflow_config.get('download_path', '/tmp/kaipoke_downloads')
    gdrive_folder_id = workflow_config.get('gdrive_folder_id')

    corporation_id = os.getenv(corporation_id_env) if corporation_id_env else None
    member_login_id = os.getenv(member_login_id_env) if member_login_id_env else None
    password = os.getenv(password_env) if password_env else None

    if not all([login_url, corporation_id, member_login_id, password]):
        logger.error("ログイン情報（URL, 事業者ID, メンバーID, パスワード）が不完全です。YAML設定と.envファイルを確認してください。")
        return

    # ダウンロードディレクトリを作成
    os.makedirs(download_path, exist_ok=True)

    drive_client = DriveClient()

    # --- メインの実行ブロック --- #
    try:
        # ブラウザを起動
        logger.info("ブラウザを起動します...")
        await browser_manager.start_browser(headless=False)
        page = await browser_manager.get_page()
        logger.info("ブラウザが正常に起動しました。")

        # 选择器执行器を初期化
        selector_executor = SelectorExecutor(page)

        # 🆕 MCPバックアップツールを初期化
        await selector_executor.initialize_mcp_fallback()

        # 初期ログイン（デフォルトアカウント）
        current_corporation_id = corporation_id
        current_member_login_id = member_login_id
        current_password = password
        login_success = await kaipoke_login_with_enhanced_selectors(page, login_url, current_corporation_id, current_member_login_id, current_password, selector_executor)

        # 按登录账号分组任务
        tasks_by_account = {}
        default_account_key = f"{current_corporation_id}_{current_member_login_id}"
        
        for task_config in config.get('tasks', []):
            params = task_config.get('params', {})
            
            # 检查是否有特殊登录信息
            task_corporation_id_env = params.get('corporation_id_env')
            task_member_login_id_env = params.get('member_login_id_env')
            task_password_env = params.get('password_env')
            
            if task_corporation_id_env and task_member_login_id_env and task_password_env:
                task_corporation_id = os.getenv(task_corporation_id_env)
                task_member_login_id = os.getenv(task_member_login_id_env)
                task_password = os.getenv(task_password_env)
                
                if task_corporation_id and task_member_login_id and task_password:
                    account_key = f"{task_corporation_id}_{task_member_login_id}"
                    if account_key not in tasks_by_account:
                        tasks_by_account[account_key] = {
                            'corporation_id': task_corporation_id,
                            'member_login_id': task_member_login_id,
                            'password': task_password,
                            'tasks': []
                        }
                    tasks_by_account[account_key]['tasks'].append(task_config)
                else:
                    # 环境变量未设置，使用默认账号
                    if default_account_key not in tasks_by_account:
                        tasks_by_account[default_account_key] = {
                            'corporation_id': current_corporation_id,
                            'member_login_id': current_member_login_id,
                            'password': current_password,
                            'tasks': []
                        }
                    tasks_by_account[default_account_key]['tasks'].append(task_config)
            else:
                # 使用默认账号
                if default_account_key not in tasks_by_account:
                    tasks_by_account[default_account_key] = {
                        'corporation_id': current_corporation_id,
                        'member_login_id': current_member_login_id,
                        'password': current_password,
                        'tasks': []
                    }
                tasks_by_account[default_account_key]['tasks'].append(task_config)
        
        logger.info(f"タスクを {len(tasks_by_account)} 個のアカウントグループに分類しました")
        
        # 各アカウントグループごとに処理
        for account_key, account_info in tasks_by_account.items():
            account_corporation_id = account_info['corporation_id']
            account_member_login_id = account_info['member_login_id']
            account_password = account_info['password']
            account_tasks = account_info['tasks']
            
            logger.info(f"=== アカウント {account_corporation_id}/{account_member_login_id} でタスクを実行 ({len(account_tasks)}個) ===")
            
            # 現在のアカウントと異なる場合は、完全にログアウトして再ログイン
            if (account_corporation_id != current_corporation_id or 
                account_member_login_id != current_member_login_id):
                
                logger.info("異なるアカウントが必要です。完全にログアウトして再ログインします...")
                
                # 完全なログアウト処理
                try:
                    # ログアウトページに移動
                    await page.goto("https://r.kaipoke.biz/kaipokebiz/login/COM020102.do", wait_until='networkidle')
                    logger.info("ログアウトページに移動しました")
                    
                    # セッションをクリア
                    await page.context.clear_cookies()
                    await page.context.clear_permissions()
                    logger.info("セッションとCookieをクリアしました")
                    
                    # 少し待機
                    await page.wait_for_timeout(2000)
                    
                except Exception as e:
                    logger.warning(f"ログアウト処理でエラー: {e}")
                
                # 新しいアカウントでログイン
                logger.info(f"新しいアカウントでログインします: {account_corporation_id}/{account_member_login_id}")
                login_success = await kaipoke_login_with_enhanced_selectors(
                    page, login_url, account_corporation_id, account_member_login_id, account_password, selector_executor
                )
                
                if not login_success:
                    logger.error(f"アカウント {account_corporation_id}/{account_member_login_id} でのログインに失敗しました")
                    continue
                
                current_corporation_id = account_corporation_id
                current_member_login_id = account_member_login_id
                current_password = account_password
                logger.info(f"✅ 新しいアカウントでのログインが完了しました")
            
            # このアカウントのタスクを実行
            for task_config in account_tasks:
                task_id = task_config.get('task_id')
                target = task_config.get('target')
                params = task_config.get('params', {})

                if not target:
                    logger.warning(f"タスクID '{task_id}' に target が定義されていません。スキップします。")
                    continue

                logger.info(f"--- タスク [{task_id}] を開始します --- ")
                logger.info(f"目標: {target}")

                try:
                    # MCP強化版でカイポケの月次実績データをダウンロード
                    downloaded_file = await download_kaipoke_monthly_report_with_enhanced_selectors(
                        selector_executor, params, download_path
                    )

                    if downloaded_file and os.path.exists(downloaded_file):
                        # Google Driveにアップロード
                        if gdrive_folder_id:
                            upload_result = drive_client.upload_file(downloaded_file, gdrive_folder_id)
                            if upload_result:
                                logger.info(f"✅ タスク '{task_id}' 完了: ファイルをGoogle Driveにアップロードしました")
                            else:
                                logger.error(f"❌ タスク '{task_id}' 失敗: Google Driveアップロードに失敗")
                        else:
                            logger.info(f"✅ タスク '{task_id}' 完了: ファイルをダウンロードしました: {downloaded_file}")
                    else:
                        logger.warning(f"⚠️ タスク '{task_id}': 配置文件选择器とMCPでのダウンロードに失敗、Agentフォールバックを実行")
                        # 選択器失効時はAgentを呼び出す
                        await call_agent_for_kaipoke_download(task_config, gdrive_folder_id)

                except Exception as e:
                    logger.error(f"❌ タスク '{task_id}' でエラーが発生しました: {e}", exc_info=True)
                    # 選択器失効時はAgentを呼び出す
                    logger.info(f"🤖 タスク '{task_id}': エラーのためAgentフォールバックを実行")
                    await call_agent_for_kaipoke_download(task_config, gdrive_folder_id)

        logger.info("すべてのタスクが完了しました。")

    except Exception as e:
        logger.error(f"ワークフローの実行中にエラーが発生しました: {e}", exc_info=True)
    finally:
        # ブラウザを閉じる
        try:
            await browser_manager.close_browser()
            logger.info("ブラウザを正常に閉じました。")
        except Exception as e:
            logger.warning(f"ブラウザの終了中にエラーが発生しました: {e}")
        logger.info("ワークフローが終了しました。")

async def kaipoke_login_with_enhanced_selectors(page, login_url, corporation_id, member_login_id, password, selector_executor):
    """カイポケにログインします（MCP強化版 - 配置文件选择器优先）。"""
    logger.info("カイポケにログインします（MCP強化版）...")

    try:
        # ログインページに移動
        await page.goto(login_url, wait_until='networkidle', timeout=60000)
        logger.info(f"ログインページにアクセスしました: {login_url}")

        # ページの内容を確認
        await page.wait_for_timeout(2000)  # 2秒待機

        # 强化された选择器执行器を使用してログイン（配置文件选择器 + MCPバックアップ）
        login_success = await selector_executor.execute_kaipoke_login(
            corporation_id, member_login_id, password
        )

        if login_success:
            logger.info("✅ カイポケへのログインが完了しました（MCP強化版）。")
            return True
        else:
            logger.warning("⚠️ 配置文件选择器とMCPでのログインに失敗、Agentフォールバックを実行")
            # Agent fallback
            await call_agent_for_kaipoke_login(page, corporation_id, member_login_id, password)
            return True

    except Exception as e:
        logger.error(f"カイポケログイン中にエラーが発生しました: {e}", exc_info=True)
        # デバッグ用にスクリーンショットを保存
        try:
            screenshot_path = '/tmp/kaipoke_login_error.png'
            await page.screenshot(path=screenshot_path)
            logger.error(f"エラー発生時のスクリーンショットを {screenshot_path} に保存しました。")
        except:
            pass

        # Agent fallback
        logger.info("🤖 エラーのためAgentフォールバックを実行")
        await call_agent_for_kaipoke_login(page, corporation_id, member_login_id, password)
        return True

async def kaipoke_login(page, login_url, corporation_id, member_login_id, password):
    """カイポケにログインします。"""
    logger.info("カイポケにログインします...")

    try:
        # ログインページに移動
        await page.goto(login_url, wait_until='networkidle', timeout=60000)
        logger.info(f"ログインページにアクセスしました: {login_url}")

        # 法人IDを入力
        await page.fill('#form\\:corporation_id', corporation_id)
        logger.info("法人IDを入力しました")

        # メンバーログインIDを入力
        await page.fill('#form\\:member_login_id', member_login_id)
        logger.info("メンバーログインIDを入力しました")

        # パスワードを入力
        await page.fill('#form\\:password', password)
        logger.info("パスワードを入力しました")

        # ログインボタンをクリック
        async with page.expect_navigation(wait_until='networkidle', timeout=60000):
            await page.click('#form\\:logn_nochklogin')
        logger.info("カイポケへのログインが完了しました")

    except Exception as e:
        logger.error(f"カイポケログイン中にエラーが発生しました: {e}", exc_info=True)
        raise

async def download_kaipoke_monthly_report_with_enhanced_selectors(selector_executor, params, download_path):
    """MCP強化版でカイポケの月次実績データをダウンロードします（配置文件选择器优先）。"""
    service_center_name = params.get('service_center_name')
    element_text = params.get('element_text')
    output_filename_pattern = params.get('output_filename_pattern')

    logger.info(f"月次実績レポートをダウンロードします（MCP強化版）: {service_center_name}")

    try:
        # ホームページに移動
        await selector_executor.page.goto("https://r.kaipoke.biz/kaipokebiz/login/COM020102.do", wait_until='networkidle')

        # 强化された选择器执行器を使用してダウンロード処理を実行（配置文件选择器 + MCPバックアップ）
        downloaded_file = await selector_executor.execute_kaipoke_monthly_report_download(
            element_text, output_filename_pattern, service_center_name, download_path
        )

        if downloaded_file:
            logger.info(f"✅ ファイルが正常にダウンロードされました（MCP強化版）: {downloaded_file}")
            return downloaded_file
        else:
            logger.warning("⚠️ 配置文件选择器とMCPでのダウンロードに失敗")
            return None

    except Exception as e:
        logger.error(f"月次実績レポートのダウンロード中にエラーが発生しました: {e}", exc_info=True)
        return None

async def download_kaipoke_monthly_report(page, params, download_path, smart_tools):
    """選択器優先でカイポケの月次実績データをダウンロードします。"""
    service_center_name = params.get('service_center_name')
    element_text = params.get('element_text')
    output_filename_pattern = params.get('output_filename_pattern')

    logger.info(f"月次実績レポートをダウンロードします: {service_center_name}")

    try:
        # レセプトメニューをクリック
        try:
            await page.click('.mainCtg li:nth-of-type(1) a', timeout=4000)
            await page.wait_for_timeout(3000)
            logger.info("レセプトメニューをクリックしました（選択器）")
        except Exception as e:
            logger.warning(f"レセプトメニューのクリックに失敗（選択器）: {e}")
            await smart_tools.intelligent_click(page, "レセプト")

        # 事業所を選択
        try:
            # XPathで事業所を探す
            await page.click(f'//a[contains(text(), "{element_text}")]', timeout=5000)
            await page.wait_for_timeout(20000)  # 20秒待機
            logger.info(f"事業所を選択しました（選択器）: {element_text}")
        except Exception as e:
            logger.warning(f"事業所の選択に失敗（選択器）: {e}")
            await smart_tools.intelligent_click(page, element_text)

        # 各種情報出力にマウスオーバー
        try:
            await page.hover('li:nth-of-type(7) img', timeout=2000)
            logger.info("各種情報出力にマウスオーバーしました")
        except Exception as e:
            logger.warning(f"マウスオーバーに失敗: {e}")

        # 出力対象選択をクリック
        try:
            await page.click('li:nth-of-type(7) li a', timeout=2000)
            await page.wait_for_timeout(1000)
            logger.info("出力対象選択をクリックしました")
        except Exception as e:
            logger.warning(f"出力対象選択のクリックに失敗: {e}")
            await smart_tools.intelligent_click(page, "出力対象選択")

        # 月次実績チップをクリック
        try:
            await page.click('#form\\:useMonthlyScheduleTip span', timeout=30000)
            logger.info("月次実績チップをクリックしました")
        except Exception as e:
            logger.warning(f"月次実績チップのクリックに失敗: {e}")

        # 前月を選択するJavaScriptを実行
        try:
            js_code = """
            var selectElement = document.querySelector("#form\\\\:serviceOfferYm");
            if (!selectElement) {
              throw new Error("選択要素が見つかりません");
            }

            var today = new Date();
            var lastMonth = new Date(today);
            lastMonth.setMonth(today.getMonth() - 1);

            var year = lastMonth.getFullYear();
            var month = lastMonth.getMonth() + 1;
            var monthStr = month < 10 ? "0" + month : month.toString();
            var targetValue = year.toString() + monthStr;

            var matchedOption = Array.from(selectElement.options).find(opt =>
              opt.value.includes(targetValue)
            );
            if (!matchedOption) {
              throw new Error("一致する年月の選択肢が見つかりません: " + targetValue);
            }

            selectElement.value = matchedOption.value;
            selectElement.dispatchEvent(new Event("change", { bubbles: true }));

            // 将年月文字设为 HTML 属性
            document.body.setAttribute("data-month-name", matchedOption.text);

            return {
              text: matchedOption.text,
              value: matchedOption.value
            };
            """
            result = await page.evaluate(js_code)
            logger.info(f"前月を選択しました: {result}")
        except Exception as e:
            logger.warning(f"前月選択JSの実行に失敗: {e}")

        # 実績区分を選択
        try:
            await page.click('#form\\:planAchieveDivision\\:1', timeout=30000)
            logger.info("実績区分を選択しました")
        except Exception as e:
            logger.warning(f"実績区分の選択に失敗: {e}")

        # Excel出力ボタンをクリック
        try:
            await page.wait_for_timeout(3000)  # 3秒待機

            # ダウンロード待機を設定
            async with page.expect_download(timeout=30000) as download_info:
                await page.click('#form\\:export img', timeout=30000)

            download = await download_info.value

            # ファイル名を生成
            from datetime import datetime, timedelta
            today = datetime.now()
            last_month = today.replace(day=1) - timedelta(days=1)

            # 令和年の計算
            reiwa_year = last_month.year - 2018  # 2019年が令和元年
            month_str = f"令和{reiwa_year}年{last_month.month:02d}月"

            if output_filename_pattern:
                filename = output_filename_pattern.format(month=month_str)
            else:
                filename = f"{month_str}{service_center_name}.xlsx"

            save_path = os.path.join(download_path, filename)
            await download.save_as(save_path)

            logger.info(f"Excel出力ボタンをクリックしました - ファイル保存: {save_path}")
            return save_path

        except Exception as e:
            logger.warning(f"Excel出力ボタンのクリックに失敗: {e}")
            await smart_tools.intelligent_click(page, "Excel出力")

            # ダウンロード完了を待機
            await page.wait_for_timeout(8000)

            # ダウンロードされたファイルを確認
            import os
            download_files = []
            if os.path.exists(download_path):
                download_files = [f for f in os.listdir(download_path) if f.endswith('.xlsx')]
                download_files.sort(key=lambda x: os.path.getctime(os.path.join(download_path, x)), reverse=True)

            if download_files:
                latest_file = download_files[0]
                logger.info(f"ダウンロード完了: {latest_file}")
                return os.path.join(download_path, latest_file)
            else:
                logger.error("ダウンロードファイルが見つかりません")
                return None

    except Exception as e:
        logger.error(f"月次実績レポートのダウンロード中にエラーが発生しました: {e}", exc_info=True)
        return None

async def call_agent_for_kaipoke_login(page, corporation_id, member_login_id, password):
    """配置文件选择器とMCP失効時にAgentを呼び出してカイポケログイン処理を行う"""
    logger.info("配置文件选择器とMCPが失効したため、Agentでカイポケログイン処理を実行します。")
    
    task = Task(
        description=f"""
        カイポケのログインページで以下の情報を使用してログインしてください：
        - 法人ID: {corporation_id}
        - メンバーログインID: {member_login_id}
        - パスワード: [設定済み]
        
        ログインフォームを見つけて各フィールドに入力し、ログインボタンをクリックしてください。
        """,
        agent=website_operator,
        expected_output="ログインが成功し、ダッシュボードまたはメインページに遷移していること。"
    )
    
    crew = Crew(
        agents=[website_operator],
        tasks=[task],
        verbose=True
    )
    
    try:
        result = crew.kickoff()
        logger.info(f"Agentによるカイポケログイン処理が完了しました: {result}")
    except Exception as e:
        logger.error(f"Agentによるカイポケログイン処理でエラーが発生しました: {e}")

async def call_agent_for_kaipoke_download(task_config, gdrive_folder_id):
    """配置文件选择器とMCP失効時にAgentを呼び出してカイポケダウンロード処理を行う"""
    logger.info("配置文件选择器とMCPが失効したため、Agentでカイポケダウンロード処理を実行します。")

    task_id = task_config.get('task_id')
    target = task_config.get('target')
    params = task_config.get('params', {})
    service_center_name = params.get('service_center_name')
    element_text = params.get('element_text')

    task = Task(
        description=f"""
        カイポケシステムで以下の手順で月次実績データをダウンロードしてください：
        1. 実績管理メニューをクリック
        2. 月次実績一覧をクリック
        3. 対象事業所「{service_center_name}」（{element_text}）を選択
        4. 前月を選択
        5. 検索ボタンをクリック
        6. Excel出力ボタンをクリックしてファイルをダウンロード
        7. ダウンロードしたファイルをGoogle Driveにアップロード

        タスクID: {task_id}
        目標: {target}
        """,
        agent=website_operator,
        expected_output="月次実績データ（Excelファイル）が正常にダウンロードされ、Google Driveの指定フォルダにアップロードされていること。"
    )

    crew = Crew(
        agents=[website_operator],
        tasks=[task],
        verbose=True
    )

    try:
        result = crew.kickoff()
        logger.info(f"Agentによるカイポケダウンロード処理が完了しました: {result}")
    except Exception as e:
        logger.error(f"Agentによるカイポケダウンロード処理でエラーが発生しました: {e}")

def run(config: dict):
    asyncio.run(async_run(config))