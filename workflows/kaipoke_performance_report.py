
import os
import asyncio
from dotenv import load_dotenv
from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.gsuite.sheets_client import SheetsClient
from core.selector_executor import SelectorExecutor
import re

# .envファイルから環境変数を読み込む
load_dotenv()

def run(config: dict):
    """カイポケ実績レポートワークフローを実行します。"""
    asyncio.run(main_async(config))

async def main_async(config: dict):
    """メインの非同期実行関数"""
    logger.info("カイポケ実績レポートワークフローを開始します（MCP強化版）。")
    
    common_config = config.get('config', {})
    tasks = config.get('tasks', [])
    
    # 共通設定を取得
    login_url = common_config.get('login_url')
    spreadsheet_id = common_config.get('spreadsheet_id')
    corp_id = os.getenv(common_config.get('corporation_id_env'))
    login_id = os.getenv(common_config.get('login_id_env'))
    password = os.getenv(common_config.get('password_env'))

    if not all([login_url, spreadsheet_id, corp_id, login_id, password]):
        logger.error("必要な共通設定（URL、スプレッドシートID、認証情報）が不足しています。")
        return

    try:
        sheets_client = SheetsClient()
        sheets_client.spreadsheet_id = spreadsheet_id
        logger.info("Google Sheets クライアントが正常に初期化されました。")
    except Exception as e:
        logger.error(f"Google Sheets クライアントの初期化に失敗しました: {e}")
        return

    # 各タスクを順番に実行
    for task_config in tasks:
        await execute_single_facility_task(task_config, common_config, sheets_client, corp_id, login_id, password)

    logger.info("✅ カイポケ実績レポートワークフローが完了しました（MCP強化版）。")

async def execute_single_facility_task(task_config: dict, common_config: dict, sheets_client: SheetsClient, corp_id, login_id, password):
    """単一の拠点に対する処理を実行する"""
    task_id = task_config.get('task_id')
    facility_name = task_config.get('facility_name')
    logger.info(f"--- タスク開始: {task_id} ({facility_name}) ---")

    # シート関連の設定
    status_cell = task_config.get('status_cell')
    target_month_cell = task_config.get('target_month_cell')
    clear_range = task_config.get('clear_range')
    
    page = None
    try:
        # 1. Google Sheetの準備
        logger.info(f"Google Sheetを準備します。対象シート: {task_config.get('target_sheet_name')}")
        sheets_client.clear_values(status_cell)
        sheets_client.append_values(status_cell, [['処理中']])
        target_month_str = sheets_client.get_cell_value(target_month_cell)
        if not target_month_str:
            raise ValueError(f"{target_month_cell}から年月を取得できませんでした。")
        logger.info(f"対象年月: {target_month_str}")
        
        # YYYYMM形式に変換
        target_ym = re.sub(r'[^0-9]', '', target_month_str)[:6]

        sheets_client.clear_values(clear_range)

        # 2. ブラウザを起動してログイン
        await browser_manager.start_browser(headless=True)
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)

        # 🆕 MCPバックアップツールを初期化
        await selector_executor.initialize_mcp_fallback()

        # ログインシーケンスを共通モジュールから呼び出す
        from core.rpa_tools.kaipoke_login_service import kaipoke_login_direct
        login_success = await kaipoke_login_direct(page, corp_id, login_id, password, common_config.get('login_url'))
        if not login_success:
            logger.error("カイポケへのログインに失敗しました（MCP強化版）。ワークフローを中断します。")
            return
        logger.info("✅ カイポケにログインしました（MCP強化版）。")
        await page.wait_for_timeout(3000)

        # 3. 目的のページまで移動
        await selector_executor.smart_click("kaipoke", "performance_report", "main_menu_receipt")
        await page.click(task_config.get('facility_selector')) # YAMLから拠点ごとのセレクタを使用
        logger.info(f"{facility_name}のページに移動しました。")
        await page.wait_for_timeout(2000)
        
        await selector_executor.smart_click("kaipoke", "performance_report", "submenu_schedule_performance")
        await selector_executor.smart_click("kaipoke", "performance_report", "submenu_schedule_performance_link")
        await page.wait_for_timeout(2000)

        # 4. 年月を選択し、条件を指定
        await selector_executor.smart_select_option("kaipoke", "performance_report", "year_month_select", value=target_ym)
        await page.wait_for_timeout(3000)
        
        await selector_executor.smart_click("kaipoke", "performance_report", "search_conditions_button")
        await selector_executor.smart_click("kaipoke", "performance_report", "checkbox_plan_data_0")
        await selector_executor.smart_click("kaipoke", "performance_report", "checkbox_achievement_data_0")
        await selector_executor.smart_click("kaipoke", "performance_report", "checkbox_plan_data_1")
        await selector_executor.smart_click("kaipoke", "performance_report", "checkbox_achievement_data_1")
        await selector_executor.smart_click("kaipoke", "performance_report", "checkbox_plan_data_2")
        await selector_executor.smart_click("kaipoke", "performance_report", "confirm_search_conditions_button")
        await page.wait_for_timeout(3000)
        await selector_executor.smart_click("kaipoke", "performance_report", "confirm_search_conditions_button") # 2回クリック
        await page.wait_for_timeout(3000)

        # 5. 全ページをループしてデータを抽出・転記
        await process_all_pages(selector_executor, sheets_client, task_config)

        # 6. 最終処理
        await finalize_sheet(sheets_client, task_config, target_month_str)
        logger.info(f"✅ タスク完了: {task_id}")

    except Exception as e:
        logger.error(f"❌ タスク '{task_id}' の実行中にエラーが発生しました（MCP強化版）: {e}", exc_info=True)
        if status_cell:
            try:
                sheets_client.append_values(status_cell, [['エラー']])
            except Exception as sheet_e:
                logger.error(f"シートへのエラー書き込みに失敗しました: {sheet_e}")
    finally:
        if page:
            await browser_manager.close_browser()
            logger.info("ブラウザを終了しました。")

async def process_all_pages(selector_executor: SelectorExecutor, sheets_client: SheetsClient, task_config: dict):
    """全ページを巡回してデータを処理する"""
    # ページ数を取得
    page_info = await selector_executor.smart_get_text("kaipoke", "performance_report", "page_info_text")
    total_pages = int(page_info.split('/')[1].replace('ページ', '').strip())
    logger.info(f"全ページ数: {total_pages}")

    for i in range(total_pages):
        current_page_num = i + 1
        logger.info(f"--- {current_page_num}/{total_pages} ページ目の処理を開始 ---")
        
        if i > 0:
            await selector_executor.smart_click("kaipoke", "performance_report", "next_page_button")
            await selector_executor.page.wait_for_timeout(3000)

        # ページ内の全利用者を処理
        from core.selectors_config import selectors_manager
        selector_config = selectors_manager.get_selector("kaipoke", "performance_report", "user_list_links")
        user_links = await selector_executor.page.locator(selector_config.primary if selector_config else ".color-neutral:nth-child(2) a").all()
        num_users = len(user_links)
        logger.info(f"{num_users}人の利用者を処理します。")

        for user_index in range(num_users):
            # 毎回セレクタを再取得してStale Element Referenceを回避
            await selector_executor.page.locator(f"({selector_executor.get_selector('kaipoke', 'performance_report', 'user_list_links')[0]})[{user_index + 1}]").click()
            await selector_executor.page.wait_for_timeout(2000)

            # データ抽出
            extracted_data = await extract_user_data(selector_executor)
            
            # Google Sheetに書き込み
            paste_range = sheets_client.find_first_blank_row(task_config.get('paste_range'))
            sheets_client.append_values(paste_range, extracted_data)
            logger.info(f"利用者 {user_index + 1} のデータをシートに書き込みました。")

            # 一覧に戻る
            await selector_executor.smart_click("kaipoke", "performance_report", "back_to_list_button")
            await selector_executor.page.wait_for_timeout(2000)

async def extract_user_data(selector_executor: SelectorExecutor) -> list:
    """利用者詳細ページからデータを抽出する"""
    # ... (データ抽出ロジックを実装)
    # RPAのロジックに基づき、必要なデータを抽出し、整形してリストのリストとして返す
    # この部分はRPAの複雑なデータ結合ロジックをPythonで再現する必要がある
    logger.warning("データ抽出ロジック(extract_user_data)は未実装です。ダミーデータを返します。")
    # ダミーデータ
    return [["日付", "曜日", "名前", "サービス内容", "保険", "提供時間", "計画時間", "", "", "", "実績サービス", "実績保険", "実績提供", "算定時間", "", "派遣人数", "備考"]]

async def finalize_sheet(sheets_client: SheetsClient, task_config: dict, target_month_str: str):
    """シートの最終処理を行う"""
    logger.info("シートの最終処理を実行します。")
    year = target_month_str.split('/')[0]
    month = target_month_str.split('/')[1]

    sheets_client.append_values(task_config.get('year_paste_cell'), [[year]])
    
    # 月を必要な行数分貼り付け
    row_count = sheets_client.get_row_count(task_config.get('target_sheet_name'))
    if row_count > 0:
        month_values = [[month]] * (row_count -1) # ヘッダー分を引くなど調整が必要
        sheets_client.update_range(task_config.get('month_paste_range'), month_values)

    sheets_client.clear_values(task_config.get('status_cell'))
    sheets_client.append_values(task_config.get('status_cell'), [['完了']])
    logger.info("ステータスを「完了」に更新しました。")
