# ==========================================================================
# 青空自动化选择器配置文件
# 为各个工作流提供选择器优先策略的配置
# ==========================================================================

# --------------------------------------------------------------------------
# Kanamic 系统选择器配置
# --------------------------------------------------------------------------
kanamic:
  login:
    username_field:
      - "#josso_username"
      - "input[name='josso_username']"
      - "input[type='text'][placeholder*='ユーザー']"
    password_field:
      - "#josso_password"
      - "input[name='josso_password']"
      - "input[type='password']"
    login_button:
      - "#form > form > div.submit-container.lastChild > input"
      - ".submit-button"
      - "input[type='submit']"
      - "button[type='submit']"
      - ".login-btn"

  # Kanamic Download 专用选择器 (基于用户提供的具体选择器)
  download:
    # 登录按钮选择器 (用户提供的具体选择器)
    login_button:
      - "#form > form > div.submit-container.lastChild > input"
      - ".submit-button"
      - "input[type='submit']"
      - "button[type='submit']"
      - ".login-btn"

    # 債権・会計菜单选择器 (用户提供的具体选择器)
    main_menu:
      - "a:nth-of-type(25) .btn"
      - "text='債権・会計'"
      - "text='業務帳票'"
      - "[href*='debt']"
      - "[href*='business']"
      - "[href*='accounting']"

    # 債権管理子菜单选择器 (用户提供的具体选择器)
    report_menu:
      - "#isA17t123-1 li:nth-of-type(2) .saikenmenu1"
      - "text='債権管理'"
      - "text='010 請求状況一覧'"
      - "[href*='debt-management']"
      - "[href*='billing']"
      - "[href*='receivable']"

    # 服务类型下拉选择器
    service_type_dropdown:
      - "#keyFlag"
      - "select[name='keyFlag']"
      - "select[id='keyFlag']"
      - "select[name='service_type']"
      - ".service-type-select"

    # 服务类型选项选择器
    service_type_option:
      - "option[value='CONTRACT_FLAG999']"
      - "option:contains('居宅介護支援')"
      - "option[value*='999']"
      - "option[text*='居宅介護支援']"

    # 复选框选择器
    checkbox_item0:
      - "#checkItem0"
      - "input[id='checkItem0']"
      - "input[name='checkItem0']"
      - "input[type='checkbox'][value*='福岡居宅']"

    checkbox_item2:
      - "#checkItem2"
      - "input[id='checkItem2']"
      - "input[name='checkItem2']"
      - "input[type='checkbox'][value*='梅ヶ丘']"

    checkbox_item4:
      - "#checkItem4"
      - "input[id='checkItem4']"
      - "input[name='checkItem4']"
      - "input[type='checkbox'][value*='居宅介護支援']"

    # 年份选择器
    date_year_select:
      - "#serviceyearkey"
      - "select[name='serviceyearkey']"
      - "select[id='serviceyearkey']"
      - "select[name='year']"
      - "#year"

    # 月份选择器
    date_month_select:
      - "#servicemonthkey"
      - "select[name='servicemonthkey']"
      - "select[id='servicemonthkey']"
      - "select[name='month']"
      - "#month"

    # 搜索按钮选择器
    search_button:
      - "#doSearch"
      - "input[id='doSearch']"
      - "button[id='doSearch']"
      - "input[type='submit'][value*='検索']"
      - "button:contains('検索')"
      - ".search-btn"

    # CSV下载按钮选择器
    download_button:
      - "#doCsv"
      - "#doCsv-2"
      - "input[id='doCsv']"
      - "input[id='doCsv-2']"
      - "button[id='doCsv']"
      - "button[id='doCsv-2']"
      - "text='CSV出力'"
      - "button:contains('CSV出力')"
      - "input[value*='CSV出力']"

  # Kanamic CSV Download 専用セレクタ
  csv_download:
    menu_link_28:
      - "a:nth-of-type(28) .btn"
      - "a:nth-of-type(28)"
      - ".menu-item:nth-child(28) a"
      - "text=分析サービス"
    button_b:
      - ".button-B"
      - "button.btn-b"
      - "[data-action='button-b']"
      - "text=LIFE用データ出力"
    service_type_select:
      - "#servicetype"
      - "select[name='servicetype']"
    service_type_option_54:
      - "option[value='54']"
    target_month_select:
      - "#targetMonth"
      - "select[name='targetMonth']"
    search_button:
      - "#searchbtn"
      - "button:contains('検索')"
      - ".search-btn"
    # 勾選用のセレクタ
    checkbox_mb5:
      - ".mb5 span"
      - ".mb5 input[type='checkbox']"
    checkbox_rowb:
      - ".rowB p:nth-of-type(2) span"
      - ".rowB input[type='checkbox']"
    # ダウンロードボタン
    download_button:
      - "#predlbtn"
      - "#dlbtn"
      - "button:contains('確認用CSV')"
      - "button:contains('ダウンロード')"
      - ".download-btn"

# --------------------------------------------------------------------------
# Kaipoke 系统选择器配置
# --------------------------------------------------------------------------
kaipoke:
  login:
    corporation_id:
      - "#form\\:corporation_id"
      - "input[name='corporation_id']"
      - "#corporation_id"
      - "input[name='corporationId']"
    member_login_id:
      - "#form\\:member_login_id"
      - "input[name='member_login_id']"
      - "#member_login_id"
      - "input[name='memberLoginId']"
    password:
      - "#form\\:password"
      - "input[name='password']"
      - "input[type='password']"
    login_button:
      - "button[onclick*='doLogin']"
      - "[onclick*='doLogin']"
      - "#form\\:logn_nochklogin"
      - "input[type='submit']"
      - "button[type='submit']"
      - "#form\\:login_button"
      - "input[type='submit']"
      - "button[type='submit']"
      - ".login-button"
    error_page_continue_button:
      - ".box-btn a"

  performance_report: # 实績レポート选择器组
    main_menu_receipt:
      - ".mainCtg li:nth-of-type(1) a"
      - "text='レセプト'"
      - "a:contains('レセプト')"
    submenu_schedule_performance:
      - "#jsddm > :nth-child(3) img"
      - "text='予定・実績'"
      - "a:contains('予定・実績')"
    submenu_schedule_performance_link:
      - "#jsddm > :nth-child(3) li a"
      - "text='予定・実績一覧'"
      - "a:contains('予定・実績一覧')"
    year_month_select:
      - "#form\\:serviceOfferYmSelectId"
      - "select[name*='serviceOfferYm']"
      - "#serviceOfferYm"
    search_conditions_button:
      - "#btn-search img"
      - "text='検索条件'"
      - "button:contains('検索条件')"
    checkbox_plan_data_0: 
      - "#form\\:create_plan_data\\:0"
      - "input[name*='create_plan_data'][value='0']"
    checkbox_achievement_data_0: 
      - "#form\\:create_achieviement_data\\:0"
      - "input[name*='create_achieviement_data'][value='0']"
    checkbox_plan_data_1: 
      - "#form\\:create_plan_data\\:1"
      - "input[name*='create_plan_data'][value='1']"
    checkbox_achievement_data_1: 
      - "#form\\:create_achieviement_data\\:1"
      - "input[name*='create_achieviement_data'][value='1']"
    checkbox_plan_data_2: 
      - "#form\\:create_plan_data\\:2"
      - "input[name*='create_plan_data'][value='2']"
    confirm_search_conditions_button:
      - ".box-search-btn div:nth-of-type(1) img"
      - "text='検索実行'"
      - "button:contains('検索実行')"
    page_info_text: 
      - ".pager-btm :nth-child(3)"
      - ".page-info"
      - ".pagination-info"
    next_page_button: 
      - ".next02 a"
      - "text='次へ'"
      - "a:contains('次へ')"
    user_list_links: 
      - ".color-neutral:nth-child(2) a"
      - ".user-link"
      - "td a[href*='user']"
    user_name_in_detail: 
      - ".txt-transition a"
      - ".user-name"
      - "h2.user-title"
    data_table_row_1_col_1: 
      - "#tableData tr:nth-of-type(1) td:nth-of-type(1)"
      - ".data-table tr:first-child td:first-child"
    data_table_row_1_col_2: 
      - "#tableData tr:nth-of-type(1) td:nth-of-type(2)"
      - ".data-table tr:first-child td:nth-child(2)"
    data_table_row_1_col_4: 
      - "#tableData tr:nth-of-type(1) :nth-child(4)"
      - ".data-table tr:first-child td:nth-child(4)"
    data_table_row_1_col_5: 
      - "#tableData tr:nth-of-type(1) :nth-child(5)"
      - ".data-table tr:first-child td:nth-child(5)"
    data_table_row_1_col_6: 
      - "#tableData tr:nth-of-type(1) :nth-child(6)"
      - ".data-table tr:first-child td:nth-child(6)"
    data_table_row_1_col_7: 
      - "#tableData tr:nth-of-type(1) :nth-child(7)"
      - ".data-table tr:first-child td:nth-child(7)"
    data_table_row_1_col_11: 
      - "#tableData tr:nth-of-type(1) :nth-child(11)"
      - ".data-table tr:first-child td:nth-child(11)"
    data_table_row_1_col_12: 
      - "#tableData tr:nth-of-type(1) :nth-child(12)"
      - ".data-table tr:first-child td:nth-child(12)"
    data_table_row_1_col_13: 
      - "#tableData tr:nth-of-type(1) :nth-child(13)"
      - ".data-table tr:first-child td:nth-child(13)"
    data_table_row_1_col_14: 
      - "#tableData tr:nth-of-type(1) :nth-child(14)"
      - ".data-table tr:first-child td:nth-child(14)"
    data_table_row_1_col_dispatch_count: 
      - "#tableData tr:nth-of-type(1) .colum09"
      - ".data-table tr:first-child .dispatch-count"
    data_table_row_1_col_remarks: 
      - "#tableData tr:nth-of-type(1) .colum06"
      - ".data-table tr:first-child .remarks"
    back_to_list_button: 
      - ".table-linkuser td:nth-of-type(3) a"
      - "text='一覧に戻る'"
      - "a:contains('一覧に戻る')"

  monthly_report:
    performance_menu:
      - "text='実績管理'"
      - "a:contains('実績管理')"
      - "[data-menu='実績管理']"
    monthly_performance_list:
      - "text='月次実績一覧'"
      - "a:contains('月次実績一覧')"
      - "[data-submenu='月次実績一覧']"
    location_dropdown:
      - "#location_select"
      - "select[name='location']"
      - ".location-select"
    previous_month_button:
      - "text='前月'"
      - "button:contains('前月')"
      - ".prev-month"
    search_button:
      - "text='検索'"
      - "button:contains('検索')"
      - "#search_btn"
      - ".search-button"
    excel_export_button:
      - "#form\\:export img"
      - "#form\\:export"
      - "text='出力する'"
      - "img[alt*='出力']"
      - "[onclick*='export']"
      - "input[value*='出力']"
    receipt_menu:
      - ".mainCtg li:nth-of-type(1) a"
      - "a:contains('レセプト')"
      - "[href*='receipt']"
      - "nav li:first-child a"
    output_target_selection:
      - "li:nth-of-type(7) li a"
      - "text='出力対象選択'"
      - "a >> text='出力対象選択'"
      - "[href*='output']"
      - "li:nth-child(7) li a"
    monthly_result_tip:
      - "#form\\:useMonthlyScheduleTip span"
      - "[id*='useMonthlyScheduleTip'] span"
      - "text='利用月間予定実績表'"
      - "span >> text='利用月間予定実績表'"
      - "[onclick*='monthly'] span"
    result_division:
      - "#form\\:planAchieveDivision\\:1"
      - "[id*='planAchieveDivision']:nth-of-type(2)"
      - "input[name*='planAchieveDivision'][value='1']"
      - "input[type='radio']:nth-of-type(2)"
    info_output_hover:
      - "li:nth-of-type(7) img"
      - "img[alt*='情報出力']"
      - "li:contains('情報出力') img"
      - "li:nth-child(7) img"
    facility_selection:
      - "//a[contains(text(), \"通所介護/4670106956\")]"
      - "text=通所介護/4670106956"
      - "a:contains('通所介護')"
      - "a:contains('4670106956')"
    service_offer_month:
      - "#form\\:serviceOfferYm"
      - "select[name*='serviceOfferYm']"
      - "select[id*='serviceOfferYm']"
      - "select[name*='month']"

# --------------------------------------------------------------------------
# 通用选择器配置
# --------------------------------------------------------------------------
common:
  buttons:
    submit:
      - "input[type='submit']"
      - "button[type='submit']"
      - ".submit-btn"
      - ".btn-submit"
    search:
      - "button:contains('検索')"
      - "input[value='検索']"
      - "#search"
      - ".search-btn"
    download:
      - "button:contains('ダウンロード')"
      - "a:contains('ダウンロード')"
      - ".download-btn"
      - "#download"
    excel:
      - "button:contains('Excel')"
      - "a:contains('Excel')"
      - ".excel-btn"
      - "#excel"

  form_elements:
    text_input:
      - "input[type='text']"
      - "input:not([type])"
    password_input:
      - "input[type='password']"
    select_dropdown:
      - "select"
    checkbox:
      - "input[type='checkbox']"
    radio:
      - "input[type='radio']"

# --------------------------------------------------------------------------
# 等待和超时配置
# --------------------------------------------------------------------------
timeouts:
  default: 30000  # 30秒
  login: 60000    # 60秒
  download: 120000 # 120秒
  navigation: 45000 # 45秒

wait_strategies:
  default: "networkidle"
  after_click: "domcontentloaded"
  after_navigation: "networkidle"
