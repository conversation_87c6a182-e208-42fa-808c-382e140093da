import os
import pandas as pd
import io
from datetime import datetime, timedelta
from dotenv import load_dotenv
from logger_config import logger
import google.auth
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# .envファイルから環境変数を読み込む（ローカル開発用）
load_dotenv()

class SheetsClient:
    """Google Sheetsとのやり取りを管理します。"""
    def __init__(self):
        """初期化時に認証情報を設定します。"""
        credentials_path = os.getenv("GCP_SERVICE_ACCOUNT_PATH")
        
        try:
            if credentials_path and os.path.exists(credentials_path):
                self.creds = service_account.Credentials.from_service_account_file(
                    credentials_path, scopes=['https://www.googleapis.com/auth/spreadsheets'])
            else:
                self.creds, _ = google.auth.default(scopes=['https://www.googleapis.com/auth/spreadsheets'])
            
            self.service = build('sheets', 'v4', credentials=self.creds)
            logger.info("SheetsClientが正常に初期化され、認証されました。")

        except Exception as e:
            logger.error(f"Google Sheetsクライアントの初期化中にエラーが発生しました: {e}", exc_info=True)
            raise

    def process_and_write_data(self, xlsx_path: str, rules: list):
        """
        YAMLルールに基づいてXLSXデータを処理し、複数のシートターゲットに書き込みます。
        """
        try:
            df = pd.read_excel(xlsx_path, header=None)

            for rule in rules:
                target_sheet_id = rule.get('target_sheet_id')
                if not target_sheet_id:
                    logger.warning("ルールに target_sheet_id がありません。スキップします。")
                    continue

                # ヘッダー行の処理
                header_row = rule.get('header_row', 1)
                if header_row > 0 and (header_row - 1) < len(df):
                    # 表头行设置为列名
                    df.columns = df.iloc[header_row - 1]
                    df_to_process = df.iloc[header_row:].copy()
                    logger.info(f"表头设置完成，列名: {list(df.columns)}")
                else:
                    df_to_process = df.copy()
                    logger.warning("未设置表头行，使用默认列名")

                # データのフィルタリング
                temp_df = df_to_process
                if 'filter_by' in rule:
                    filter_col = rule['filter_by']['column']
                    filter_val = rule['filter_by']['value']
                    if filter_col in temp_df.columns:
                        temp_df = temp_df[temp_df[filter_col].astype(str).str.contains(filter_val, na=False)]
                    else:
                        logger.warning(f"フィルタリング列 '{filter_col}' が見つかりません。Sheet ID: {target_sheet_id} のフィルタリングをスキップします。")

                if temp_df.empty:
                    logger.warning(f"フィルタリング後、データが空です。Sheet ID: {target_sheet_id} への書き込みをスキップします。")
                    continue

                # A列からAP列まで（42列）を抽出
                if temp_df.shape[1] >= 42:
                    temp_df_to_paste = temp_df.iloc[:, :42]
                else:
                    logger.warning(f"データ列が42未満です。あるだけの列を貼り付けます。Shape: {temp_df.shape}")
                    temp_df_to_paste = temp_df

                # 貼り付け開始行の処理
                paste_start_row = rule.get('paste_start_row', 2)

                # NaN値を空文字列に置換してからリストに変換
                temp_df_to_paste = temp_df_to_paste.fillna('')
                data_to_paste = temp_df_to_paste.values.tolist()

                # 貼り付け前のセル更新
                if 'pre_paste_updates' in rule:
                    for update in rule['pre_paste_updates']:
                        cell = update['cell']
                        value_source = update['value_source']
                        
                        if value_source == 'previous_month':
                            prev_month = (datetime.now().replace(day=1) - timedelta(days=1)).strftime('%Y/%m/01')
                            self.write_to_sheet(target_sheet_id, cell, [[prev_month]])
                        elif value_source == 'csv_column' and 'source_details' in update:
                            col_name = update['source_details']
                            if col_name in df.columns: # Use original df to get header-based values
                                value = df[col_name].iloc[header_row] # Get first data row value
                                if col_name == '提供年月':
                                    try:
                                        # 处理不同的日期格式
                                        if isinstance(value, str):
                                            if '/' in value:
                                                # 格式如 "2025/05"
                                                date_obj = pd.to_datetime(value, format='%Y/%m')
                                            else:
                                                # 其他格式尝试自动解析
                                                date_obj = pd.to_datetime(value)
                                        else:
                                            date_obj = pd.to_datetime(value)
                                        value = date_obj.strftime('%Y/%m/01')
                                    except Exception as e:
                                        logger.warning(f"日付形式の変換に失敗しました: {value}, エラー: {e}")
                                        # 如果转换失败，尝试直接使用原值
                                        if isinstance(value, str) and '/' in value:
                                            # 如果是 "2025/05" 格式，直接添加 "/01"
                                            if value.count('/') == 1:
                                                value = value + '/01'
                                self.write_to_sheet(target_sheet_id, cell, [[value]])
                
                # データ本体の貼り付け
                paste_range = f"A{paste_start_row}"
                self.write_to_sheet(target_sheet_id, paste_range, data_to_paste)

        except Exception as e:
            logger.error(f"データ処理と書き込み中にエラーが発生しました: {e}", exc_info=True)


    def read_sheet(self, spreadsheet_id: str, range_name: str):
        """指定された範囲からデータを読み取ります。"""
        logger.info(f"スプレッドシート {spreadsheet_id} の範囲 {range_name} からデータを読み取ります...")
        try:
            result = self.service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id, range=range_name).execute()
            values = result.get('values', [])
            logger.info(f"{len(values)}行のデータを正常に読み取りました。")
            return values
        except HttpError as error:
            logger.error(f"シートからの読み取り中にAPIエラーが発生しました: {error}")
            return None
        except Exception as e:
            logger.error(f"シートからの読み取り中に予期せぬエラーが発生しました: {e}", exc_info=True)
            return None

    def write_to_sheet(self, spreadsheet_id: str, range_name: str, values: list):
        """指定された範囲にデータを書き込みます。"""
        logger.info(f"スプレッドシート {spreadsheet_id} の範囲 {range_name} にデータを書き込みます...")
        try:
            body = {'values': values}
            result = self.service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id, range=range_name,
                valueInputOption='USER_ENTERED', body=body).execute()
            logger.info(f"{result.get('updatedCells')}個のセルが正常に更新されました。")
            return result
        except HttpError as error:
            logger.error(f"シートへの書き込み中にAPIエラーが発生しました: {error}")
            return None
        except Exception as e:
            logger.error(f"シートへの書き込み中に予期せぬエラーが発生しました: {e}", exc_info=True)
            return None

    def clear_values(self, range_name: str):
        """指定された範囲のデータをクリアします。"""
        try:
            self.service.spreadsheets().values().clear(
                spreadsheetId=self.spreadsheet_id, range=range_name).execute()
            logger.info(f"範囲 {range_name} のデータをクリアしました。")
        except Exception as e:
            logger.error(f"データクリア中にエラーが発生しました: {e}")

    def append_values(self, range_name: str, values: list):
        """指定された範囲にデータを追加します。"""
        try:
            body = {'values': values}
            result = self.service.spreadsheets().values().update(
                spreadsheetId=self.spreadsheet_id, range=range_name,
                valueInputOption='USER_ENTERED', body=body).execute()
            logger.info(f"範囲 {range_name} にデータを追加しました。")
            return result
        except Exception as e:
            logger.error(f"データ追加中にエラーが発生しました: {e}")
            return None

    def get_cell_value(self, range_name: str):
        """指定されたセルの値を取得します。"""
        try:
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id, range=range_name).execute()
            values = result.get('values', [])
            if values and values[0]:
                return values[0][0]
            return None
        except Exception as e:
            logger.error(f"セル値取得中にエラーが発生しました: {e}")
            return None

    def find_first_blank_row(self, range_name: str):
        """指定された範囲で最初の空白行を見つけます。"""
        try:
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id, range=range_name).execute()
            values = result.get('values', [])
            return f"{range_name.split('!')[0]}!A{len(values) + 1}"
        except Exception as e:
            logger.error(f"空白行検索中にエラーが発生しました: {e}")
            return range_name

    def get_row_count(self, sheet_name: str):
        """指定されたシートの行数を取得します。"""
        try:
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id, range=f"{sheet_name}!A:A").execute()
            values = result.get('values', [])
            return len(values)
        except Exception as e:
            logger.error(f"行数取得中にエラーが発生しました: {e}")
            return 0

    def update_range(self, range_name: str, values: list):
        """指定された範囲を更新します。"""
        try:
            body = {'values': values}
            result = self.service.spreadsheets().values().update(
                spreadsheetId=self.spreadsheet_id, range=range_name,
                valueInputOption='USER_ENTERED', body=body).execute()
            logger.info(f"範囲 {range_name} を更新しました。")
            return result
        except Exception as e:
            logger.error(f"範囲更新中にエラーが発生しました: {e}")
            return None
