import argparse
import yaml
import importlib
import os
from dotenv import load_dotenv # 追加
from logger_config import logger

# .env ファイルをロード
load_dotenv(dotenv_path=os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env'))

logger.debug(f"DEBUG: KAIPOKE_CORPORATION_ID = {os.getenv('KAIPOKE_CORPORATION_ID')}")
logger.debug(f"DEBUG: KAIPOKE_MEMBER_LOGIN_ID = {os.getenv('KAIPOKE_MEMBER_LOGIN_ID')}")
logger.debug(f"DEBUG: KAIPOKE_PASSWORD = {os.getenv('KAIPOKE_PASSWORD')}")

def main():
    """プロジェクトのエントリーポイント。コマンドライン引数を解析し、対応するワークフローを実行します。"""
    # コマンドライン引数のパーサーを作成
    parser = argparse.ArgumentParser(description="Aozora Automated Workflows Runner")
    parser.add_argument(
        'workflow_id',
        type=str,
        help='実行するワークフローのID (例: kaipoke_monthly_report)',
        choices=get_available_workflows() # 利用可能なワークフローを動的に取得
    )

    args = parser.parse_args()
    logger.info(f"要求されたワークフロー: {args.workflow_id}")

    # 設定ファイルからワークフローの設定を読み込む
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'configs', 'workflows.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            all_configs = yaml.safe_load(f)
    except FileNotFoundError:
        logger.error(f"設定ファイル {config_path} が見つかりません。")
        return
    except yaml.YAMLError as e:
        logger.error(f"設定ファイルの読み込み中にエラーが発生しました: {e}")
        return

    workflow_config = all_configs.get(args.workflow_id)
    if not workflow_config:
        logger.error(f"ワークフローID '{args.workflow_id}' の設定が configs/workflows.yaml に見つかりません。")
        return

    # 対応するワークフローモジュールを動的にインポートして実行
    try:
        logger.info(f"ワークフローモジュール 'workflows.{args.workflow_id}' を読み込んでいます...")
        # 例: workflows.kaipoke_monthly_report をインポート
        workflow_module = importlib.import_module(f"workflows.{args.workflow_id}")
        logger.debug(f"DEBUG: workflow_id for function call: {args.workflow_id}")
        
        # モジュール内の run 関数を実行し、設定を渡す
        workflow_module.run(workflow_config)
        
        logger.info(f"ワークフロー '{args.workflow_id}' が正常に完了しました。")

    except ImportError as e:
        logger.error(f"ワークフローモジュール workflows/{args.workflow_id}.py が見つからないか、インポートエラーがあります: {e}")
    except AttributeError as e:
        logger.error(f"ワークフローモジュールに run(config) 関数が定義されていません: {e}")
        logger.error(f"利用可能な属性: {dir(workflow_module)}")
    except Exception as e:
        logger.error(f"ワークフロー '{args.workflow_id}' の実行中に予期せぬエラーが発生しました: {e}", exc_info=True)

def get_available_workflows():
    """YAML設定ファイルから利用可能なワークフローIDのリストを取得します。"""
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'configs', 'workflows.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            configs = yaml.safe_load(f)
            return list(configs.keys()) if configs else []
    except FileNotFoundError:
        return []
    except yaml.YAMLError as e:
        logger.error(f"設定ファイルの読み込み中にエラーが発生しました: {e}")
        return []

if __name__ == "__main__":
    main()